# Setting Up Latest Wagmi in Nuxt.js

## Installation

To set up Wagmi in your Nuxt.js project, you'll need to install the required packages:

```bash
npm install wagmi viem @tanstack/vue-query
# or
yarn add wagmi viem @tanstack/vue-query
# or
pnpm add wagmi viem @tanstack/vue-query
```

## Configuration

### 1. Create Wagmi Config

Create a `composables/wagmi.ts` file to configure Wagmi:

```typescript
// composables/wagmi.ts
import { http, createConfig } from 'wagmi'
import { mainnet, sepolia } from 'wagmi/chains'
import { injected, metaMask, safe, walletConnect } from 'wagmi/connectors'

const projectId = 'YOUR_PROJECT_ID' // Get from WalletConnect Cloud

export const config = createConfig({
  chains: [mainnet, sepolia],
  connectors: [
    injected(),
    metaMask(),
    safe(),
    walletConnect({ projectId }),
  ],
  transports: {
    [mainnet.id]: http(),
    [sepolia.id]: http(),
  },
})
```

### 2. Create Vue Query Client Plugin

Create a `plugins/vue-query.client.ts` file:

```typescript
// plugins/vue-query.client.ts
import { VueQueryPlugin, QueryClient } from '@tanstack/vue-query'

export default defineNuxtPlugin((nuxtApp) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 5, // 5 minutes
      },
    },
  })

  nuxtApp.vueApp.use(VueQueryPlugin, { queryClient })
})
```

### 3. Create Wagmi Plugin

Create a `plugins/wagmi.client.ts` file:

```typescript
// plugins/wagmi.client.ts
import { WagmiPlugin } from '@wagmi/vue'
import { config } from '~/composables/wagmi'

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.use(WagmiPlugin, { config })
})
```

## Usage Example

### Basic Connection Component

```vue


  
    
      Connected to {{ address }}
      Balance: {{ balance?.formatted }} {{ balance?.symbol }}
      Disconnect
    
    
      Connect Wallet
      
        {{ connector.name }}
      
    
  



import { useAccount, useBalance, useConnect, useDisconnect } from '@wagmi/vue'

const { address, isConnected } = useAccount()
const { connect, connectors, isPending } = useConnect()
const { disconnect } = useDisconnect()

const { data: balance } = useBalance({
  address: address,
})

```

### Reading Contract Data

```vue


  
    Contract Data
    
      Total Supply: {{ data }}
    
    Loading...
    Error: {{ error.message }}
  



import { useReadContract } from '@wagmi/vue'

const { data, isLoading, error } = useReadContract({
  address: '0x...',
  abi: [
    {
      name: 'totalSupply',
      type: 'function',
      stateMutability: 'view',
      inputs: [],
      outputs: [{ type: 'uint256' }],
    },
  ],
  functionName: 'totalSupply',
})

```

## Important Notes

### SSR Considerations

Since Wagmi interacts with browser APIs, make sure to:

1. Use `.client.ts` suffix for plugins to ensure they only run on the client side
2. Wrap Wagmi hooks in `` components when necessary:

```vue

  
    
  

```

### Environment Variables

Add your WalletConnect Project ID to your `.env` file:

```env
NUXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_project_id_here
```

Then update your Wagmi config:

```typescript
const projectId = useRuntimeConfig().public.walletconnectProjectId
```

## Additional Configuration

### Nuxt Config

Update your `nuxt.config.ts` if needed:

```typescript
export default defineNuxtConfig({
  ssr: true,
  css: ['~/assets/css/main.css'],
  runtimeConfig: {
    public: {
      walletconnectProjectId: process.env.NUXT_PUBLIC_WALLETCONNECT_PROJECT_ID
    }
  }
})
```

This setup provides you with a fully functional Wagmi integration in your Nuxt.js application, allowing you to connect wallets, read contract data, and interact with the Ethereum blockchain seamlessly.
