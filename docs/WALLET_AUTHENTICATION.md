# Wallet Authentication System

## Overview

The DeFi Agent now includes a comprehensive wallet authentication system using SIWE (Sign-In with Ethereum) with EIP-712 compliant message signing. This provides secure, decentralized authentication without requiring traditional OAuth or JWT tokens.

## Features

### 🔐 **SIWE Authentication**
- EIP-712 compliant message signing for secure authentication
- 24-hour session persistence with automatic expiry
- Client-side signature verification
- Proper nonce generation and message formatting

### 🎯 **Three-State UI System**
1. **Disconnected**: User has no wallet connected
2. **Connected but Not Authenticated**: Wallet connected but user hasn't signed authentication message
3. **Fully Authenticated**: Wallet connected and user has signed authentication message

### 🔄 **Complete Login/Logout Flow**
- Connect wallet → Sign authentication message → Authenticated session
- Logout clears both authentication state and disconnects wallet
- Automatic cleanup on wallet disconnection or address change

### 📱 **Mobile-Responsive Design**
- Neo-Brutalism design system integration
- Responsive layouts for all screen sizes
- Touch-friendly interactions

## Implementation Details

### Core Files

#### `app/composables/useAuth.ts`
- Main authentication composable
- Handles SIWE message generation and signing
- Manages authentication state persistence
- Provides reactive authentication status

#### `app/composables/wagmi.ts`
- Wagmi v2 configuration with Ethereum mainnet and Sepolia
- Wallet connectors: MetaMask, WalletConnect, Injected, Safe
- HTTP transport configuration

#### `app/components/WalletConnect.vue`
- Enhanced wallet connection component
- Three-state UI implementation
- SIWE authentication integration
- Neo-Brutalism styling

### Authentication Flow

```mermaid
graph TD
    A[Disconnected] --> B[Click Connect Wallet]
    B --> C[Select Wallet Connector]
    C --> D[Wallet Connected]
    D --> E[Click Sign In]
    E --> F[Sign SIWE Message]
    F --> G[Fully Authenticated]
    G --> H[Click Logout]
    H --> A
    D --> I[Click Disconnect]
    I --> A
```

### State Management

The authentication system maintains the following state:

```typescript
interface AuthState {
  isAuthenticated: boolean
  address: string | null
  message: string | null
  signature: string | null
  timestamp: number | null
}
```

### Session Persistence

- Authentication state is stored in `localStorage`
- Sessions expire after 24 hours
- Automatic cleanup on wallet disconnection
- Cross-tab synchronization

## Usage

### Basic Implementation

```vue
<template>
  <ClientOnly>
    <WalletConnect />
  </ClientOnly>
</template>
```

### Accessing Authentication State

```typescript
import { useAuth } from '@/composables/useAuth'

const { 
  isAuthenticated, 
  authenticatedAddress,
  signIn,
  signOut 
} = useAuth()
```

### Wallet Connection State

```typescript
import { useAccount } from '@wagmi/vue'

const { address, isConnected } = useAccount()
```

## Environment Configuration

### Required Environment Variables

```bash
# WalletConnect Project ID (get from https://cloud.walletconnect.com/)
NUXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_project_id_here
```

### Runtime Configuration

The WalletConnect Project ID is configured in `nuxt.config.ts`:

```typescript
runtimeConfig: {
  public: {
    walletconnectProjectId: process.env.NUXT_PUBLIC_WALLETCONNECT_PROJECT_ID || ''
  }
}
```

## Testing

### Manual Testing Checklist

#### 1. Wallet Connection
- [ ] Click "CONNECT WALLET" button
- [ ] Modal opens with available connectors
- [ ] Can select and connect with MetaMask
- [ ] Can select and connect with WalletConnect
- [ ] Error handling for connection failures
- [ ] Modal closes after successful connection

#### 2. Authentication Flow
- [ ] After wallet connection, shows "SIGN IN" button
- [ ] Click "SIGN IN" triggers wallet signature request
- [ ] Successful signature shows "✓ AUTHENTICATED" status
- [ ] Error handling for signature rejection
- [ ] Authentication state persists on page refresh

#### 3. Logout Flow
- [ ] "LOGOUT" button appears when authenticated
- [ ] Click "LOGOUT" clears authentication and disconnects wallet
- [ ] UI returns to disconnected state
- [ ] Authentication state is cleared from storage

#### 4. Mobile Responsiveness
- [ ] Component works on mobile devices
- [ ] Modal is properly sized on small screens
- [ ] Buttons are touch-friendly
- [ ] Text is readable on all screen sizes

#### 5. Error Handling
- [ ] Network errors are handled gracefully
- [ ] Wallet rejection errors show user-friendly messages
- [ ] Invalid signatures are handled properly
- [ ] Expired sessions are cleaned up automatically

### Automated Testing

```bash
# Run development server
bun run dev

# Test wallet connections with browser dev tools
# Check localStorage for authentication state
# Verify signature verification
```

## Security Considerations

### SIWE Message Format

The authentication message follows the SIWE standard:

```
defi-ai.com wants you to sign in with your Ethereum account:
0x1234...5678

Sign in to DEFI.AI to access your AI-powered DeFi portfolio management.

URI: https://defi-ai.com
Version: 1
Chain ID: 1
Nonce: abc123def456
Issued At: 2024-01-01T00:00:00.000Z
Expiration Time: 2024-01-02T00:00:00.000Z
```

### Security Features

- **Nonce**: Prevents replay attacks
- **Expiration**: 24-hour session limit
- **Domain Binding**: Messages are bound to the application domain
- **Chain ID**: Ensures correct network
- **Address Verification**: Signature verification confirms wallet ownership

## Troubleshooting

### Common Issues

1. **WalletConnect not working**: Ensure `NUXT_PUBLIC_WALLETCONNECT_PROJECT_ID` is set
2. **Signature verification fails**: Check that the wallet is connected to the correct network
3. **Authentication state not persisting**: Verify localStorage is enabled
4. **Modal not appearing**: Check for JavaScript errors in console

### Debug Mode

Enable debug logging by adding to your `.env`:

```bash
NUXT_LOG_LEVEL=debug
```

## Next Steps

1. **Add WalletConnect Project ID**: Get a project ID from [WalletConnect Cloud](https://cloud.walletconnect.com/)
2. **Test with Real Wallets**: Test the complete flow with MetaMask and WalletConnect
3. **Add Backend Verification**: Implement server-side signature verification for enhanced security
4. **Add More Connectors**: Consider adding Coinbase Wallet, Rainbow, etc.
5. **Implement Session Management**: Add session refresh and background verification
