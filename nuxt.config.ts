import tailwindcss from '@tailwindcss/vite';
import oxlintPlugin from 'vite-plugin-oxlint';
export default defineNuxtConfig({
  css: ['./assets/css/tailwind.css'],
  vite: {
    plugins: [tailwindcss(), oxlintPlugin()],
  },
  modules: ['@nuxthub/core', 'nuxt-mcp', 'shadcn-nuxt', '@vueuse/motion/nuxt'],
  shadcn: {
    /**
     * Prefix for all the imported component
     */
    prefix: '',
    /**
     * Directory that the component lives in.
     * @default "./components/ui"
     */
    componentDir: './app/components/ui',
  },

  motion: {
    directives: {
      'pop-bottom': {
        initial: {
          scale: 0,
          opacity: 0,
          y: 100,
        },
        visible: {
          scale: 1,
          opacity: 1,
          y: 0,
          transition: {
            type: 'spring',
            stiffness: 260,
            damping: 20,
            duration: 300,
          },
        },
      },
      'slide-left': {
        initial: {
          x: -100,
          opacity: 0,
        },
        visible: {
          x: 0,
          opacity: 1,
          transition: {
            type: 'spring',
            stiffness: 260,
            damping: 20,
            duration: 300,
          },
        },
      },
      'slide-right': {
        initial: {
          x: 100,
          opacity: 0,
        },
        visible: {
          x: 0,
          opacity: 1,
          transition: {
            type: 'spring',
            stiffness: 260,
            damping: 20,
            duration: 300,
          },
        },
      },
      'brutal-bounce': {
        initial: {
          scale: 0.8,
          opacity: 0,
          rotate: -5,
        },
        visible: {
          scale: 1,
          opacity: 1,
          rotate: 0,
          transition: {
            type: 'spring',
            stiffness: 400,
            damping: 10,
            duration: 400,
          },
        },
      },
    },
  },
  devtools: { enabled: true },

  runtimeConfig: {
    public: {
      helloText: 'Hello from the Edge 👋',
      walletconnectProjectId:
        process.env.NUXT_PUBLIC_WALLETCONNECT_PROJECT_ID || '',
    },
  },
  future: { compatibilityVersion: 4 },
  compatibilityDate: '2025-03-01',
  hub: {},
});
