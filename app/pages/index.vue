<script setup lang="ts">
// SEO and meta
useHead({
  title: 'DEFI.AI - AI-Powered DeFi Portfolio Management',
  meta: [
    {
      name: 'description',
      content:
        'Dominate DeFi with AI-powered portfolio management. Automated strategies, yield optimization, and risk management for Ethereum protocols.',
    },
  ],
});

// Feature data
const features = [
  {
    title: 'AI STRATEGY ENGINE',
    description:
      'Advanced reinforcement learning algorithms optimize your DeFi positions across Aave, Uniswap V3, and Lido.',
    accent: 'neon-lime',
    icon: 'brain',
  },
  {
    title: 'AUTOMATED REBALANCING',
    description:
      'Smart contract automation executes optimal portfolio adjustments based on market conditions and AI insights.',
    accent: 'neon-cyan',
    icon: 'refresh-cw',
  },
  {
    title: 'YIELD HARVESTING',
    description:
      'Maximize returns with automated reward claiming and compound optimization across multiple protocols.',
    accent: 'neon-orange',
    icon: 'trending-up',
  },
  {
    title: 'RISK MANAGEMENT',
    description:
      'Real-time monitoring and automated protection mechanisms safeguard your portfolio against market volatility and smart contract risks.',
    accent: 'laser-red',
    icon: 'shield-check',
  },
];

// Performance metrics (mock data)
const performanceMetrics = [
  { label: 'AVG APY', value: '24.7%', change: '+12.3%' },
  { label: 'TOTAL VALUE', value: '$2.4M', change: '+45.2%' },
  { label: 'PROTOCOLS', value: '3', change: 'ACTIVE' },
  { label: 'RISK SCORE', value: '6.2/10', change: 'MODERATE' },
];
</script>

<template>
  <div class="min-h-screen bg-brutal-white">
    <!-- Hero Section -->
    <section class="relative overflow-hidden bg-brutal-white container-spacing section-spacing">
      <div class="mx-auto max-w-7xl">
        <div class="text-center mobile-margin-x">
          <!-- Main Headline -->
          <h1 class="font-brutal mobile-heading text-brutal-black shadow-brutal-lg glitch">
            AI-POWERED
            <br />
            <span class="text-electric-blue">DEFI DOMINATION</span>
          </h1>

          <!-- Subheading -->
          <p class="font-mono-brutal mx-auto mt-6 sm:mt-8 max-w-3xl mobile-text text-brutal-black">
            <span class="block sm:inline">&gt; AUTOMATED PORTFOLIO MANAGEMENT FOR THE FUTURE OF FINANCE</span>
            <br class="hidden sm:block" />
            <span class="block sm:inline mt-2 sm:mt-0">&gt; MAXIMIZE YIELDS • MINIMIZE RISK • OPTIMIZE EVERYTHING</span>
          </p>

          <!-- CTA Button -->
          <div class="mt-8 sm:mt-12 flex justify-center">
            <ClientOnly>
              <WalletConnect />
            </ClientOnly>
          </div>

          <!-- Performance Metrics -->
          <div class="mt-brutal-2xl mobile-grid gap-brutal-lg">
            <div
              v-for="(metric, index) in performanceMetrics"
              :key="metric.label"
              class="border-brutal bg-brutal-white p-brutal-lg shadow-brutal hover-brutal-neon mobile-tap"
              :class="{
                'shadow-brutal-electric-blue': index === 0,
                'shadow-brutal-hot-magenta': index === 1,
                'shadow-brutal-acid-green': index === 2,
                'shadow-brutal-laser-red': index === 3
              }"
            >
              <div class="font-mono-brutal text-xs sm:text-sm text-brutal-black">{{ metric.label }}</div>
              <div class="font-brutal text-2xl sm:text-3xl text-brutal-black">{{ metric.value }}</div>
              <div
                class="font-mono-brutal text-xs sm:text-sm"
                :class="{
                  'text-electric-blue': index === 0,
                  'text-hot-magenta': index === 1,
                  'text-acid-green': index === 2,
                  'text-laser-red': index === 3
                }"
              >
                {{ metric.change }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="border-brutal-heavy bg-brutal-charcoal container-spacing section-spacing">
      <div class="mx-auto max-w-7xl">
        <div class="text-center mobile-margin-x">
          <h2 class="font-brutal text-3xl sm:text-4xl md:text-5xl lg:text-6xl text-brutal-white">
            FEATURES THAT
            <span class="text-acid-green">DOMINATE</span>
          </h2>
        </div>

        <div class="mt-brutal-2xl mobile-grid gap-brutal-xl">
          <div
            v-for="(feature, index) in features"
            :key="feature.title"
            class="border-brutal bg-brutal-white p-brutal-lg shadow-brutal hover-brutal-electric mobile-tap"
            :class="{
              'shadow-brutal-neon-lime': index === 0,
              'shadow-brutal-neon-cyan': index === 1,
              'shadow-brutal-plasma-orange': index === 2,
              'shadow-brutal-laser-red': index === 3
            }"
          >
            <div class="mb-brutal-lg">
              <div
                class="inline-flex h-12 w-12 sm:h-16 sm:w-16 items-center justify-center border-brutal touch-target"
                :class="{
                  'bg-neon-lime': index === 0,
                  'bg-neon-cyan': index === 1,
                  'bg-plasma-orange': index === 2,
                  'bg-laser-red': index === 3
                }"
              >
                <Icon :name="'lucide:' + feature.icon" class="h-6 w-6 sm:h-8 sm:w-8 text-brutal-black" />
              </div>
            </div>
            <h3 class="font-brutal text-lg sm:text-xl text-brutal-black">{{ feature.title }}</h3>
            <p class="font-mono-brutal mt-brutal mobile-text text-brutal-black">
              {{ feature.description }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Terminal Section -->
    <section class="bg-brutal-black container-spacing section-spacing">
      <div class="mx-auto max-w-7xl">
        <div class="text-center mobile-margin-x">
          <h2 class="font-brutal text-3xl sm:text-4xl md:text-5xl lg:text-6xl text-brutal-white">
            LIVE PORTFOLIO
            <span class="text-plasma-orange">ANALYTICS</span>
          </h2>
        </div>

        <div class="mt-brutal-2xl">
          <div class="border-brutal bg-brutal-black p-brutal-xl shadow-brutal-acid-green">
            <div class="font-mono-brutal text-acid-green">
              <div class="typing text-sm sm:text-base">$ defi-ai portfolio --analyze --optimize</div>
              <div class="mt-4 text-brutal-white mobile-text">
                <div class="space-y-1">
                  <div>&gt; Analyzing portfolio composition...</div>
                  <div>&gt; Current APY: <span class="text-electric-blue">24.7%</span> (+12.3% vs benchmark)</div>
                  <div>&gt; Risk Score: <span class="text-hot-magenta">6.2/10</span> (MODERATE)</div>
                  <div>&gt; Optimization opportunities detected: <span class="text-toxic-yellow">3</span></div>
                </div>
                <br />
                <div class="space-y-1">
                  <div>&gt; RECOMMENDATION: Rebalance 15% from AAVE to LIDO</div>
                  <div>&gt; EXPECTED IMPACT: <span class="text-neon-lime">****% APY</span>, <span class="text-neon-cyan">-0.3 risk score</span></div>
                  <div>&gt; GAS COST: ~$45 | BREAK-EVEN: 3.2 days</div>
                </div>
                <br />
                <span class="text-acid-green">✓ Ready to execute optimization</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="border-brutal-heavy bg-acid-green container-spacing section-spacing">
      <div class="mx-auto max-w-7xl text-center mobile-margin-x">
        <h2 class="font-brutal text-3xl sm:text-4xl md:text-5xl lg:text-6xl text-brutal-black">
          START DOMINATING
          <br />
          DEFI TODAY
        </h2>
        <p class="font-mono-brutal mx-auto mt-brutal-lg max-w-2xl mobile-text text-brutal-black">
          Join the future of automated portfolio management. Connect your wallet and let AI optimize your DeFi strategy.
        </p>
        <div class="mt-brutal-2xl flex justify-center">
          <ClientOnly>
            <WalletConnect />
          </ClientOnly>
        </div>
      </div>
    </section>
  </div>
</template>
