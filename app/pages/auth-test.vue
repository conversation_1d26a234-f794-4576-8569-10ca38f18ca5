<template>
  <div class="min-h-screen bg-brutal-black p-6">
    <div class="max-w-4xl mx-auto">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="font-mono-brutal text-3xl text-electric-blue mb-4">
          WALLET AUTHENTICATION TEST
        </h1>
        <p class="font-mono-brutal text-sm text-brutal-white">
          Test the complete SIWE authentication flow
        </p>
      </div>

      <!-- Wallet Connection Component -->
      <div class="mb-8 p-6 bg-brutal-charcoal border-2 border-electric-blue">
        <h2 class="font-mono-brutal text-xl text-neon-lime mb-4">
          WALLET CONNECTION
        </h2>
        <div class="flex justify-center">
          <ClientOnly>
            <WalletConnect />
          </ClientOnly>
        </div>
      </div>

      <!-- Authentication State Display -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Wallet State -->
        <div class="p-6 bg-brutal-charcoal border-2 border-neon-lime">
          <h3 class="font-mono-brutal text-lg text-neon-lime mb-4">
            WALLET STATE
          </h3>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="font-mono-brutal text-sm text-brutal-white">Connected:</span>
              <span class="font-mono-brutal text-sm" :class="isConnected ? 'text-acid-green' : 'text-hot-magenta'">
                {{ isConnected ? 'YES' : 'NO' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="font-mono-brutal text-sm text-brutal-white">Address:</span>
              <span class="font-mono-brutal text-sm text-brutal-white">
                {{ address ? formatAddress(address) : 'N/A' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="font-mono-brutal text-sm text-brutal-white">Chain ID:</span>
              <span class="font-mono-brutal text-sm text-brutal-white">
                {{ chainId || 'N/A' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="font-mono-brutal text-sm text-brutal-white">Balance:</span>
              <span class="font-mono-brutal text-sm text-neon-lime">
                {{ balance ? `${parseFloat(balance.formatted).toFixed(4)} ${balance.symbol}` : 'N/A' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Authentication State -->
        <div class="p-6 bg-brutal-charcoal border-2 border-hot-magenta">
          <h3 class="font-mono-brutal text-lg text-hot-magenta mb-4">
            AUTH STATE
          </h3>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="font-mono-brutal text-sm text-brutal-white">Authenticated:</span>
              <span class="font-mono-brutal text-sm" :class="isAuthenticated ? 'text-acid-green' : 'text-hot-magenta'">
                {{ isAuthenticated ? 'YES' : 'NO' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="font-mono-brutal text-sm text-brutal-white">Auth Address:</span>
              <span class="font-mono-brutal text-sm text-brutal-white">
                {{ authenticatedAddress ? formatAddress(authenticatedAddress) : 'N/A' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="font-mono-brutal text-sm text-brutal-white">Session Valid:</span>
              <span class="font-mono-brutal text-sm" :class="sessionValid ? 'text-acid-green' : 'text-hot-magenta'">
                {{ sessionValid ? 'YES' : 'NO' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="font-mono-brutal text-sm text-brutal-white">Signing:</span>
              <span class="font-mono-brutal text-sm" :class="isSigningIn ? 'text-toxic-yellow' : 'text-brutal-white'">
                {{ isSigningIn ? 'YES' : 'NO' }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Manual Actions -->
      <div class="p-6 bg-brutal-charcoal border-2 border-toxic-yellow">
        <h3 class="font-mono-brutal text-lg text-toxic-yellow mb-4">
          MANUAL ACTIONS
        </h3>
        <div class="flex flex-wrap gap-4">
          <Button
            @click="handleManualSignIn"
            :disabled="!isConnected || isAuthenticated || isSigningIn"
            class="bg-acid-green hover:bg-acid-green/80 text-brutal-black font-mono-brutal"
          >
            MANUAL SIGN IN
          </Button>
          <Button
            @click="handleManualSignOut"
            :disabled="!isAuthenticated || isSigningOut"
            class="bg-hot-magenta hover:bg-hot-magenta/80 text-brutal-black font-mono-brutal"
          >
            MANUAL SIGN OUT
          </Button>
          <Button
            @click="clearStorage"
            class="bg-brutal-white hover:bg-brutal-white/80 text-brutal-black font-mono-brutal"
          >
            CLEAR STORAGE
          </Button>
          <Button
            @click="refreshData"
            class="bg-electric-blue hover:bg-electric-blue/80 text-brutal-black font-mono-brutal"
          >
            REFRESH DATA
          </Button>
        </div>
      </div>

      <!-- Debug Information -->
      <div class="mt-8 p-6 bg-brutal-charcoal border-2 border-brutal-white">
        <h3 class="font-mono-brutal text-lg text-brutal-white mb-4">
          DEBUG INFO
        </h3>
        <div class="space-y-2">
          <div>
            <span class="font-mono-brutal text-sm text-brutal-white">Last Error:</span>
            <p class="font-mono-brutal text-xs text-hot-magenta mt-1">
              {{ lastError || 'None' }}
            </p>
          </div>
          <div>
            <span class="font-mono-brutal text-sm text-brutal-white">Available Connectors:</span>
            <p class="font-mono-brutal text-xs text-brutal-white mt-1">
              {{ connectors.map(c => c.name).join(', ') || 'None' }}
            </p>
          </div>
          <div>
            <span class="font-mono-brutal text-sm text-brutal-white">Storage Data:</span>
            <pre class="font-mono-brutal text-xs text-brutal-white mt-1 bg-brutal-black p-2 border border-brutal-white overflow-auto">{{ storageData }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAccount, useBalance, useConnect, useChainId } from '@wagmi/vue'
import { useAuth } from '@/composables/useAuth'
import { Button } from '@/components/ui/button'

// Set page meta
definePageMeta({
  title: 'Authentication Test',
  description: 'Test wallet authentication flow'
})

// Wallet state
const { address, isConnected } = useAccount()
const { data: balance } = useBalance({ address })
const { connectors } = useConnect()
const chainId = useChainId()

// Authentication state
const { 
  isAuthenticated, 
  authenticatedAddress,
  signIn, 
  signOut, 
  isSigningIn, 
  isSigningOut 
} = useAuth()

// Local state
const lastError = ref<string | null>(null)
const storageData = ref<string>('')

// Computed
const sessionValid = computed(() => {
  return isAuthenticated.value && authenticatedAddress.value === address.value
})

// Methods
const formatAddress = (addr: string) => {
  return `${addr.slice(0, 6)}...${addr.slice(-4)}`
}

const handleManualSignIn = async () => {
  try {
    lastError.value = null
    await signIn()
  } catch (error: any) {
    lastError.value = error.message || 'Sign in failed'
  }
}

const handleManualSignOut = async () => {
  try {
    lastError.value = null
    await signOut()
  } catch (error: any) {
    lastError.value = error.message || 'Sign out failed'
  }
}

const clearStorage = () => {
  if (process.client) {
    localStorage.removeItem('defi-ai-auth')
    refreshData()
  }
}

const refreshData = () => {
  if (process.client) {
    const stored = localStorage.getItem('defi-ai-auth')
    storageData.value = stored ? JSON.stringify(JSON.parse(stored), null, 2) : 'No data'
  }
}

// Initialize
onMounted(() => {
  refreshData()
})

// Watch for changes
watch([isAuthenticated, address], () => {
  refreshData()
})
</script>
