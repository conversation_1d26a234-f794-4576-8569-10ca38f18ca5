import { http, createConfig } from 'wagmi'
import { mainnet, sepolia } from 'wagmi/chains'
import { injected, metaMask, safe, walletConnect } from 'wagmi/connectors'

// Get WalletConnect Project ID from runtime config
const getProjectId = () => {
  if (process.client) {
    const config = useRuntimeConfig()
    return config.public.walletconnectProjectId
  }
  return process.env.NUXT_PUBLIC_WALLETCONNECT_PROJECT_ID || ''
}

export const config = createConfig({
  chains: [mainnet, sepolia],
  connectors: [
    injected(),
    metaMask(),
    safe(),
    walletConnect({ 
      projectId: getProjectId(),
      metadata: {
        name: 'DEFI.AI',
        description: 'AI-Powered DeFi Portfolio Management',
        url: 'https://defi-ai.com',
        icons: ['https://defi-ai.com/icon.png']
      }
    }),
  ],
  transports: {
    [mainnet.id]: http(),
    [sepolia.id]: http(),
  },
})

declare module 'wagmi' {
  interface Register {
    config: typeof config
  }
}
