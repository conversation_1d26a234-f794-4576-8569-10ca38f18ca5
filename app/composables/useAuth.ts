import { SiweMessage } from 'siwe'
import { useAccount, useSignMessage } from '@wagmi/vue'

export interface AuthState {
  isAuthenticated: boolean
  address: string | null
  message: string | null
  signature: string | null
  timestamp: number | null
}

const AUTH_STORAGE_KEY = 'defi-ai-auth'
const AUTH_EXPIRY_HOURS = 24

export const useAuth = () => {
  const { address, isConnected } = useAccount()
  const { signMessageAsync } = useSignMessage()

  // Reactive authentication state
  const authState = ref<AuthState>({
    isAuthenticated: false,
    address: null,
    message: null,
    signature: null,
    timestamp: null,
  })

  // Loading states
  const isSigningIn = ref(false)
  const isSigningOut = ref(false)

  // Initialize auth state from storage
  const initializeAuth = () => {
    if (!process.client) return

    try {
      const stored = localStorage.getItem(AUTH_STORAGE_KEY)
      if (stored) {
        const parsedAuth: AuthState = JSON.parse(stored)
        
        // Check if auth is expired
        const now = Date.now()
        const authAge = now - (parsedAuth.timestamp || 0)
        const maxAge = AUTH_EXPIRY_HOURS * 60 * 60 * 1000
        
        if (authAge < maxAge && parsedAuth.address === address.value) {
          authState.value = parsedAuth
        } else {
          // Clear expired auth
          clearAuthState()
        }
      }
    } catch (error) {
      console.error('Failed to initialize auth state:', error)
      clearAuthState()
    }
  }

  // Clear authentication state
  const clearAuthState = () => {
    authState.value = {
      isAuthenticated: false,
      address: null,
      message: null,
      signature: null,
      timestamp: null,
    }
    
    if (process.client) {
      localStorage.removeItem(AUTH_STORAGE_KEY)
    }
  }

  // Save authentication state
  const saveAuthState = (state: AuthState) => {
    authState.value = state
    
    if (process.client) {
      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(state))
    }
  }

  // Generate SIWE message
  const generateSiweMessage = async (walletAddress: string): Promise<string> => {
    const domain = process.client ? window.location.host : 'defi-ai.com'
    const origin = process.client ? window.location.origin : 'https://defi-ai.com'
    
    const message = new SiweMessage({
      domain,
      address: walletAddress,
      statement: 'Sign in to DEFI.AI to access your AI-powered DeFi portfolio management.',
      uri: origin,
      version: '1',
      chainId: 1, // Ethereum mainnet
      nonce: Math.random().toString(36).substring(2, 15),
      issuedAt: new Date().toISOString(),
      expirationTime: new Date(Date.now() + AUTH_EXPIRY_HOURS * 60 * 60 * 1000).toISOString(),
    })

    return message.prepareMessage()
  }

  // Sign in with Ethereum
  const signIn = async (): Promise<boolean> => {
    if (!address.value || !isConnected.value) {
      throw new Error('Wallet not connected')
    }

    try {
      isSigningIn.value = true

      // Generate SIWE message
      const message = await generateSiweMessage(address.value)

      // Sign the message
      const signature = await signMessageAsync({ message })

      // Verify the signature (basic client-side verification)
      const siweMessage = new SiweMessage(message)
      const verificationResult = await siweMessage.verify({ signature })

      if (verificationResult.success) {
        // Save authentication state
        const newAuthState: AuthState = {
          isAuthenticated: true,
          address: address.value,
          message,
          signature,
          timestamp: Date.now(),
        }

        saveAuthState(newAuthState)
        return true
      } else {
        throw new Error('Signature verification failed')
      }
    } catch (error) {
      console.error('Sign in failed:', error)
      throw error
    } finally {
      isSigningIn.value = false
    }
  }

  // Sign out
  const signOut = async (): Promise<void> => {
    try {
      isSigningOut.value = true
      clearAuthState()
    } finally {
      isSigningOut.value = false
    }
  }

  // Watch for wallet disconnection
  watch(
    [isConnected, address],
    ([connected, currentAddress]) => {
      if (!connected || currentAddress !== authState.value.address) {
        clearAuthState()
      }
    }
  )

  // Initialize on mount
  onMounted(() => {
    initializeAuth()
  })

  return {
    // State
    authState: readonly(authState),
    isAuthenticated: computed(() => authState.value.isAuthenticated),
    authenticatedAddress: computed(() => authState.value.address),
    
    // Loading states
    isSigningIn: readonly(isSigningIn),
    isSigningOut: readonly(isSigningOut),
    
    // Methods
    signIn,
    signOut,
    clearAuthState,
  }
}
