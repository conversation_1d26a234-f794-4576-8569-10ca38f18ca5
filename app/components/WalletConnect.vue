import { useAccount, useBalance, useConnect, useDisconnect } from '@wagmi/vue';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/composables/useAuth';

// Wallet connection state
const { address, isConnected } = useAccount();
const { connect, connectors, isPending, error } = useConnect();
const { disconnect } = useDisconnect();

// Authentication state
const { isAuthenticated, signIn, signOut, isSigningIn, isSigningOut } =
  useAuth();

// Balance query
const { data: balance } = useBalance({
  address: address,
});

// Local state
const showConnectModal = ref(false);
const isConnecting = ref(false);
const isDisconnecting = ref(false);
const authError = ref<string | null>(null);

// Methods
const openConnectModal = () => {
  showConnectModal.value = true;
  authError.value = null;
};

const closeModal = () => {
  showConnectModal.value = false;
  authError.value = null;
};

const connectWallet = async (connector: any) => {
  try {
    isConnecting.value = true;
    await connect({ connector });
    showConnectModal.value = false;
  } catch (err) {
    console.error('Failed to connect wallet:', err);
    authError.value = 'Failed to connect wallet';
  } finally {
    isConnecting.value = false;
  }
};

const handleSignIn = async () => {
  try {
    authError.value = null;
    await signIn();
  } catch (err: any) {
    console.error('Failed to sign in:', err);
    authError.value = err.message || 'Failed to sign message';
  }
};

const handleDisconnect = async () => {
  try {
    isDisconnecting.value = true;
    await disconnect();
  } catch (err) {
    console.error('Failed to disconnect:', err);
  } finally {
    isDisconnecting.value = false;
  }
};

const handleLogout = async () => {
  try {
    await signOut();
    await disconnect();
  } catch (err) {
    console.error('Failed to logout:', err);
  }
};

const formatAddress = (addr: string) => {
  return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
};

// Watch for connection changes
watch(isConnected, (connected) => {
  if (connected) {
    showConnectModal.value = false;
    isConnecting.value = false;
  }
});
