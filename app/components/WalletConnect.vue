import { useAccount, useBalance, useConnect, useDisconnect } from '@wagmi/vue';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

// Wallet connection state
const { address, isConnected } = useAccount();
const { connect, connectors, isPending, error } = useConnect();
const { disconnect } = useDisconnect();

// Balance query
const { data: balance } = useBalance({
  address: address,
});

// Local state
const showConnectModal = ref(false);
const isConnecting = ref(false);
const isDisconnecting = ref(false);

// Methods
const openConnectModal = () => {
  showConnectModal.value = true;
};

const connectWallet = async (connector: any) => {
  try {
    isConnecting.value = true;
    await connect({ connector });
    showConnectModal.value = false;
  } catch (err) {
    console.error('Failed to connect wallet:', err);
  } finally {
    isConnecting.value = false;
  }
};

const formatAddress = (addr: string) => {
  return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
};

// Watch for connection changes
watch(isConnected, (connected) => {
  if (connected) {
    showConnectModal.value = false;
    isConnecting.value = false;
  }
});
