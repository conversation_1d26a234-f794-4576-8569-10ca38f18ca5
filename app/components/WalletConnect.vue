<template>
  <div class="wallet-connect">
    <!-- Connected State -->
    <div v-if="isConnected && address" class="connected-state">
      <div class="wallet-info">
        <span class="font-mono-brutal text-sm text-brutal-white">
          {{ formatAddress(address) }}
        </span>
        <span v-if="balance" class="font-mono-brutal text-xs text-neon-lime">
          {{ parseFloat(balance.formatted).toFixed(4) }} {{ balance.symbol }}
        </span>
      </div>
      <Button
        @click="disconnect"
        variant="destructive"
        size="sm"
        class="bg-hot-magenta hover:bg-hot-magenta/80 text-brutal-black font-mono-brutal text-xs"
        :disabled="isDisconnecting"
      >
        <span v-if="isDisconnecting">DISCONNECTING...</span>
        <span v-else>DISCONNECT</span>
      </Button>
    </div>

    <!-- Disconnected State -->
    <div v-else class="disconnected-state">
      <Button
        @click="openConnectModal"
        variant="default"
        size="default"
        class="bg-electric-blue hover:bg-electric-blue/80 text-brutal-black font-mono-brutal"
        :disabled="isConnecting"
      >
        <span v-if="isConnecting">CONNECTING...</span>
        <span v-else>CONNECT WALLET</span>
      </Button>
    </div>

    <!-- Simple Modal for Connectors -->
    <div v-if="showConnectModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <h3 class="font-mono-brutal text-xl text-electric-blue mb-4">
          CONNECT YOUR WALLET
        </h3>
        <p class="font-mono-brutal text-sm text-brutal-white mb-6">
          Choose your preferred wallet to connect to DEFI.AI
        </p>

        <div class="connector-list">
          <button
            v-for="connector in connectors"
            :key="connector.uid"
            @click="connectWallet(connector)"
            :disabled="isPending || !connector.ready"
            class="connector-button"
          >
            <span class="font-mono-brutal text-sm text-brutal-white">
              {{ connector.name }}
            </span>
            <span v-if="!connector.ready" class="font-mono-brutal text-xs text-hot-magenta">
              Not Installed
            </span>
          </button>
        </div>

        <div v-if="error" class="error-message">
          <p class="font-mono-brutal text-sm text-hot-magenta">
            {{ error.message }}
          </p>
        </div>

        <button @click="closeModal" class="close-button">
          <span class="font-mono-brutal text-sm text-brutal-white">CLOSE</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { useAccount, useBalance, useConnect, useDisconnect } from '@wagmi/vue'

// Wallet connection state
const { address, isConnected } = useAccount()
const { connect, connectors, isPending, error } = useConnect()
const { disconnect } = useDisconnect()

// Balance query
const { data: balance } = useBalance({
  address: address,
})

// Local state
const showConnectModal = ref(false)
const isConnecting = ref(false)
const isDisconnecting = ref(false)

// Methods
const openConnectModal = () => {
  showConnectModal.value = true
}

const closeModal = () => {
  showConnectModal.value = false
}

const connectWallet = async (connector: any) => {
  try {
    isConnecting.value = true
    await connect({ connector })
    showConnectModal.value = false
  } catch (err) {
    console.error('Failed to connect wallet:', err)
  } finally {
    isConnecting.value = false
  }
}

const formatAddress = (addr: string) => {
  return `${addr.slice(0, 6)}...${addr.slice(-4)}`
}

// Watch for connection changes
watch(isConnected, (connected) => {
  if (connected) {
    showConnectModal.value = false
    isConnecting.value = false
  }
})
</script>

<style scoped>
.wallet-connect {
  display: flex;
  align-items: center;
  gap: 8px;
}

.connected-state {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: var(--color-brutal-black);
  border: 2px solid var(--color-electric-blue);
}

.wallet-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-content {
  background-color: var(--color-brutal-black);
  border: 2px solid var(--color-electric-blue);
  padding: 24px;
  max-width: 400px;
  width: 90%;
}

.connector-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.connector-button {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  padding: 16px;
  background-color: var(--color-brutal-black);
  border: 2px solid var(--color-neon-lime);
  transition: border-color 0.2s;
}

.connector-button:hover:not(:disabled) {
  border-color: var(--color-electric-blue);
}

.connector-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.error-message {
  margin-top: 16px;
  padding: 12px;
  background-color: rgba(255, 0, 110, 0.1);
  border: 1px solid var(--color-hot-magenta);
}

.close-button {
  margin-top: 16px;
  padding: 8px 16px;
  background-color: var(--color-brutal-charcoal);
  border: 2px solid var(--color-brutal-white);
  width: 100%;
}

.close-button:hover {
  background-color: var(--color-brutal-white);
  color: var(--color-brutal-black);
}
</style>